import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, ChangeDetectionStrategy, Component, ViewChild, inject, signal } from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { CoreAngularModule } from '@ecs/angular';
import { UiFailedToSaveDialogComponent } from '@shared/ui';

interface LabelOption {
  id: string;
  name: string;
  description?: string;
}

interface VerificationResult {
  validPackages: string[];
  invalidPackages: string[];
  totalFound: number;
  totalInvalid: number;
}

@Component({
  selector: 'esky-pps-nx-mass-labeling',
  standalone: true,
  imports: [
    CommonModule,
    CoreAngularModule,
    ReactiveFormsModule,
    UiFailedToSaveDialogComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './mass-labeling.component.html',
  styleUrls: ['./mass-labeling.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MassLabelingComponent {
  @ViewChild('failedToSaveDialog') failedToSaveDialog!: UiFailedToSaveDialogComponent;
  private fb = inject(FormBuilder);
  private router = inject(Router);

  // Signals for reactive state management
  isVerified = signal(false);
  isLoading = signal(false);
  isSuccess = signal(false);
  verificationResult = signal<VerificationResult | null>(null);
  availableLabels = signal<LabelOption[]>([
    { id: 'critical', name: 'Critical', description: 'Requires immediate attention' },
    { id: 'urgent', name: 'Urgent', description: 'High priority issue' },
    { id: 'follow-up', name: 'Follow-up', description: 'Requires follow-up action' },
    { id: 'resolved', name: 'Resolved', description: 'Issue has been resolved' },
  ]);

  // Template variables for expandable sections
  showValidPackages = false;
  showInvalidPackages = false;
  selectedFileName: string | null = null;

  // Form for package input
  packageForm = this.fb.group({
    inputMethod: ['manual', Validators.required],
    packageNumbers: [''],
    csvFile: [null as File | null],
  });

  // Form for label configuration
  labelForm = this.fb.group({
    selectedLabel: ['', Validators.required],
    description: ['', [Validators.required, Validators.maxLength(240)]],
    labelAction: ['add', Validators.required], // 'add', 'replace', 'remove'
  });

  triggerFileInput(event: Event): void {
    event.preventDefault();
    const fileInput = document.getElementById('csv-file') as HTMLInputElement;
    fileInput?.click();
  }

  onFileSelected(event: Event): void {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];

    if (file && file.type === 'text/csv') {
      this.packageForm.patchValue({ csvFile: file });
      this.selectedFileName = file.name;
    }
  }

  onVerifyAndProceed(): void {
    // Check if at least one input method has data
    const packageNumbers = this.packageForm.get('packageNumbers')?.value || '';
    const csvFile = this.packageForm.get('csvFile')?.value;

    if (!packageNumbers.trim() && !csvFile) {
      return; // No input provided
    }

    this.isLoading.set(true);

    // Simulate API call for verification
    setTimeout(() => {
      let packageNumbersArray: string[] = [];

      if (packageNumbers.trim()) {
        // Manual input
        packageNumbersArray = packageNumbers
          .split(/[,\n\r\s]+/)
          .map(num => num.trim())
          .filter(num => num.length > 0);
      } else if (csvFile) {
        // For file upload, simulate parsing CSV
        packageNumbersArray = ['1234567', '2367890', '3456789']; // Mock data
      }

      // Mock verification result
      const validPackages = packageNumbersArray.filter((_, index) => index % 4 !== 3); // 3/4 valid
      const invalidPackages = packageNumbersArray.filter((_, index) => index % 4 === 3); // 1/4 invalid

      this.verificationResult.set({
        validPackages,
        invalidPackages,
        totalFound: validPackages.length,
        totalInvalid: invalidPackages.length,
      });

      this.isVerified.set(true);
      this.isLoading.set(false);
    }, 1500);
  }

  onApplyLabels(): void {
    if (this.labelForm.invalid || !this.verificationResult()) {
      return;
    }

    this.isLoading.set(true);

    // Simulate API call for applying labels
    setTimeout(() => {
      console.log('Applying labels:', {
        packages: this.verificationResult()?.validPackages,
        label: this.labelForm.get('selectedLabel')?.value,
        description: this.labelForm.get('description')?.value,
        action: this.labelForm.get('labelAction')?.value,
      });

      // Simulate successful operation most of the time
      if (Math.random() > 0.2) {
        this.isLoading.set(false);
        this.isSuccess.set(true);
        
        // Reset after 3 seconds
        setTimeout(() => {
          this.onClear();
        }, 3000);
      } else {
        // Simulate error scenario
        this.isLoading.set(false);
        this.failedToSaveDialog.open([{ message: 'Failed to apply labels to some packages. Please try again.' }]);
      }
    }, 2000);
  }

  onClear(): void {
    this.packageForm.reset({ inputMethod: 'manual' });
    this.labelForm.reset({ labelAction: 'add' });
    this.isVerified.set(false);
    this.isSuccess.set(false);
    this.verificationResult.set(null);
  }

  downloadCsvTemplate(): void {
    const csvContent = 'package_number\n1234567\n2345678\n3456789';
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'package_numbers_template.csv';
    link.click();
    window.URL.revokeObjectURL(url);
  }

  backToLabels(): void {
    this.router.navigate(['/labels']);
  }
}
