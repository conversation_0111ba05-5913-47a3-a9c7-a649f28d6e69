@import 'libs/shared/ui/src/assets/styles/form';

// Main container styles
.main-container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-l);
  background-color: var(--color-gray-05);
  min-height: 100vh;
}

// Page header styles
.page-header {
  margin-bottom: var(--spacing-xl);

  .page-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-gray-140);
    margin: 0 0 var(--spacing-xs) 0;
  }

  .page-subtitle {
    font-size: var(--font-size-base);
    color: var(--color-gray-80);
    margin: 0;
  }
}

// White card styles
.white-card {
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-20);
  border-radius: var(--border-radius-m);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:last-child {
    margin-bottom: 0;
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

.card-title {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-140);
  margin: 0 0 var(--spacing-s) 0;
}

.card-description {
  font-size: var(--font-size-base);
  color: var(--color-gray-80);
  margin: 0 0 var(--spacing-xl) 0;
  line-height: 1.5;
}

// Option section styles
.option-section {
  margin-bottom: var(--spacing-l);

  .option-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    color: var(--color-gray-140);
    margin: 0 0 var(--spacing-m) 0;
  }

  .option-content {
    margin-left: var(--spacing-l);

    .field-label {
      display: block;
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-140);
      margin-bottom: var(--spacing-s);
    }

    .help-text {
      display: block;
      font-size: var(--font-size-s);
      color: var(--color-gray-80);
      margin-top: var(--spacing-s);
    }
  }
}

// OR separator styles
.or-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: var(--spacing-xl) 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--color-gray-30);
  }

  .or-text {
    background-color: var(--color-white);
    padding: 0 var(--spacing-m);
    font-size: var(--font-size-s);
    color: var(--color-gray-80);
    font-weight: var(--font-weight-medium);
  }
}

// File upload styles
.file-upload-field {
  display: flex !important;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #d1d5db !important;
  border-radius: 4px;
  background-color: #ffffff !important;
  cursor: pointer;
  margin-bottom: 8px;
  min-height: 40px;
  max-width: 50%;
  transition: border-color 0.2s ease;

  &:hover {
    border-color: var(--color-gray-50);
  }

  &:focus-within {
    border-color: var(--color-primary-50);
    outline: 2px solid var(--color-primary-10);
    outline-offset: -1px;
  }

  .file-input {
    display: none;
  }

  .file-status {
    flex: 1;
    color: var(--color-gray-80);
    font-size: var(--font-size-base);
  }

  .choose-file-text {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--color-primary-50);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    flex-shrink: 0;

    ecs-icon {
      color: var(--color-primary-50);
    }
  }
}

.download-template-btn {
  margin-top: var(--spacing-m);

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.success-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-success);
  background-color: var(--color-success-light);
  padding: var(--spacing-s) var(--spacing-m);
  border-radius: var(--border-radius-s);

  ecs-icon {
    color: var(--color-success);
  }
}

.disabled-content {
  opacity: 0.6;
  pointer-events: none;
}

.muted-text {
  color: var(--color-gray-60);
  font-style: italic;
}

.pending-verification {
  padding: var(--spacing-m);
  background-color: var(--color-gray-05);
  border-radius: var(--border-radius-s);
  text-align: center;
}

.mass-labeling-container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl);
  background-color: var(--color-white);
  min-height: 100vh;
}

.header-section {
  margin-bottom: var(--spacing-xxl);
  
  h2 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-gray-140);
    margin: 0 0 var(--spacing-xs) 0;
  }
  
  .subtitle {
    font-size: var(--font-size-base);
    color: var(--color-gray-80);
    margin: 0;
  }
}

.section-card {
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-05);
  border-radius: var(--border-radius-s);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  position: relative;
  
  &.disabled {
    opacity: 0.6;
    pointer-events: none;
    
    .disabled-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--border-radius-s);
      
      p {
        font-size: var(--font-size-base);
        color: var(--color-gray-80);
        margin: 0;
      }
    }
  }
}

.section-header {
  margin-bottom: var(--spacing-xl);
  
  h3 {
    font-size: var(--font-size-l);
    font-weight: var(--font-weight-bold);
    color: var(--color-gray-140);
    margin: 0 0 var(--spacing-s) 0;
  }
  
  .section-description {
    font-size: var(--font-size-base);
    color: var(--color-gray-80);
    margin: 0;
    line-height: 1.5;
  }
}

.input-option {
  margin-bottom: var(--spacing-xl);
  
  .option-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-m);
    
    .radio-input {
      margin-right: var(--spacing-s);
      accent-color: var(--color-primary-50);
    }
    
    .option-label {
      font-size: var(--font-size-base);
      color: var(--color-gray-140);
      cursor: pointer;
      margin: 0;
    }
  }
  
  .option-content {
    margin-left: var(--spacing-xl);
    
    .field-label {
      display: block;
      font-size: var(--font-size-s);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-140);
      margin-bottom: var(--spacing-xs);
    }
    
    .package-textarea {
      width: 100%;
      min-height: 100px;
      padding: var(--spacing-s);
      border: 1px solid var(--color-gray-40);
      border-radius: var(--border-radius-xxxs);
      font-family: var(--font-family-base);
      font-size: var(--font-size-base);
      resize: vertical;
      
      &:focus {
        outline: none;
        border-color: var(--color-primary-50);
      }
    }
    
    .help-text {
      display: block;
      font-size: var(--font-size-xs);
      color: var(--color-gray-80);
      margin-top: var(--spacing-xs);
    }
  }
}

.file-upload-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-m);
  
  .file-input {
    flex: 1;
    padding: var(--spacing-s);
    border: 1px solid var(--color-gray-40);
    border-radius: var(--border-radius-xxxs);
    
    &:focus {
      outline: none;
      border-color: var(--color-primary-50);
    }
  }
}

.divider {
  height: 1px;
  background-color: var(--color-gray-05);
  margin: var(--spacing-xl) 0;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-m);
  margin-top: var(--spacing-xl);
}

.btn {
  padding: var(--spacing-s) var(--spacing-l);
  border-radius: var(--border-radius-xxxs);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  border: none;
  transition: all 0.2s ease;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.btn-primary {
    background-color: var(--color-primary-50);
    color: var(--color-white);
    
    &:hover:not(:disabled) {
      background-color: var(--color-primary-60);
    }
  }
  
  &.btn-secondary {
    background-color: var(--color-gray-05);
    color: var(--color-gray-140);
    border: 1px solid var(--color-gray-40);
    
    &:hover:not(:disabled) {
      background-color: var(--color-gray-10);
    }
  }
  
  &.btn-link {
    background: none;
    color: var(--color-primary-50);
    padding: var(--spacing-xs) 0;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.verification-section {
  .verification-results {
    margin-bottom: var(--spacing-xxl);
    
    h4 {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-140);
      margin: 0 0 var(--spacing-m) 0;
    }
    
    .result-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-s);
      margin-bottom: var(--spacing-s);
      
      &.success {
        color: var(--color-success-50);
      }
      
      &.error {
        color: var(--color-error-50);
      }
    }
  }
}

.expandable-section {
  margin-top: var(--spacing-m);
  
  .expand-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: none;
    border: none;
    color: var(--color-primary-50);
    font-size: var(--font-size-base);
    cursor: pointer;
    padding: var(--spacing-xs) 0;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  .package-list {
    margin-top: var(--spacing-s);
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--color-gray-05);
    border-radius: var(--border-radius-xxxs);
    
    .package-item {
      padding: var(--spacing-xs) var(--spacing-s);
      border-bottom: 1px solid var(--color-gray-05);
      font-family: var(--font-family-mono);
      font-size: var(--font-size-s);
      
      &:last-child {
        border-bottom: none;
      }
      
      &.error {
        color: var(--color-error-50);
        background-color: var(--color-error-01);
      }
    }
  }
}

.label-configuration {
  h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    color: var(--color-gray-140);
    margin: 0 0 var(--spacing-m) 0;
  }
  
  .radio-group {
    margin-bottom: var(--spacing-xl);
    
    .radio-option {
      margin-bottom: var(--spacing-m);
      
      .radio-input {
        margin-right: var(--spacing-s);
        accent-color: var(--color-primary-50);
      }
      
      label {
        font-size: var(--font-size-base);
        color: var(--color-gray-140);
        cursor: pointer;
        margin: 0;
      }
      
      .warning-text {
        display: block;
        font-size: var(--font-size-xs);
        color: var(--color-warning-50);
        margin-top: var(--spacing-xs);
        margin-left: var(--spacing-xl);
      }
    }
  }
  
  .label-selection {
    margin-bottom: var(--spacing-xl);
    
    .form-field {
      margin-bottom: var(--spacing-l);
      
      .field-label {
        display: block;
        font-size: var(--font-size-s);
        font-weight: var(--font-weight-medium);
        color: var(--color-gray-140);
        margin-bottom: var(--spacing-xs);
      }
      
      .select-input {
        width: 100%;
        padding: var(--spacing-s);
        border: 1px solid var(--color-gray-40);
        border-radius: var(--border-radius-xxxs);
        font-size: var(--font-size-base);
        background-color: var(--color-white);
        
        &:focus {
          outline: none;
          border-color: var(--color-primary-50);
        }
      }
      
      .description-textarea {
        width: 100%;
        min-height: 80px;
        padding: var(--spacing-s);
        border: 1px solid var(--color-gray-40);
        border-radius: var(--border-radius-xxxs);
        font-family: var(--font-family-base);
        font-size: var(--font-size-base);
        resize: vertical;
        
        &:focus {
          outline: none;
          border-color: var(--color-primary-50);
        }
      }
      
      .char-count {
        display: block;
        font-size: var(--font-size-xs);
        color: var(--color-gray-80);
        text-align: right;
        margin-top: var(--spacing-xs);
      }
    }
    
    .add-label-btn {
      color: var(--color-primary-50);
      background: none;
      border: none;
      font-size: var(--font-size-base);
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      padding: 0;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
