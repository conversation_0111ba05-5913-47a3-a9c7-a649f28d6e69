<div class="main-container">
  <ecs-button
    class="mb-m"
    [text]="true"
    icon="arrow_left"
    kind="primary"
    size="small"
    data-selector="mass-labeling-back-btn"
    (click)="backToLabels()"
  >
    back to labels
  </ecs-button>

  <!-- Page Header -->
  <div class="page-header">
    <h1 class="page-title">Mass packages labeling</h1>
    <p class="page-subtitle">Apply labels to packages</p>
  </div>

  <!-- Card 1: Provide package numbers -->
  <div class="white-card">
    <h2 class="card-title">Provide package numbers</h2>
    <p class="card-description">Use this page to apply labels to multiple packages at once. You can provide the package numbers by typing/pasting them below or by uploading a CSV file.</p>
      <form [formGroup]="packageForm">
        <!-- Option 1: Enter numbers manually -->
        <div class="option-section">
          <h3 class="option-title">Option 1: Enter numbers manually</h3>
          <div class="option-content">
            <label class="field-label">Package numbers:</label>
            <ecs-input
              formControlName="packageNumbers"
              class="form-field-container size-xxl"
              [textarea]="true"
              [rows]="4"
              placeholder="1234567, 23678"
            ></ecs-input>
            <small class="help-text">
              E.g., 1234567, 7654321, 9876543 or list each number on a new line
            </small>
          </div>
        </div>

        <!-- OR Separator -->
        <div class="or-separator">
          <span class="or-text">OR</span>
        </div>

        <!-- Option 2: Upload a file -->
        <div class="option-section">
          <h3 class="option-title">Option 2: Upload a file</h3>
          <div class="option-content">
            <label class="field-label">Select CSV file:</label>
            <label for="csv-file" class="file-upload-field">
              <input
                type="file"
                id="csv-file"
                accept=".csv"
                (change)="onFileSelected($event)"
                class="file-input">
              <span class="file-status">{{ selectedFileName || 'No file chosen' }}</span>
              <span class="choose-file-text">
                <ecs-icon name="upload" size="small"></ecs-icon>
                Choose file
              </span>
            </label>
            <small class="help-text">
              Accepted format: .csv file should contain one reservation number per row in a single column.
            </small>
            <ecs-button
              [text]="true"
              kind="primary"
              icon="download"
              size="small"
              (click)="downloadCsvTemplate()"
              class="download-template-btn"
            >
              Download CSV template
            </ecs-button>
          </div>
        </div>

        <!-- Hidden radio group for form control -->
        <div style="display: none;">
          <ecs-radio-group formControlName="inputMethod">
            <ecs-radio value="manual" />
            <ecs-radio value="file" />
          </ecs-radio-group>
        </div>

        <div class="d-flex">
          <ecs-button
            type="button"
            data-selector="mass-labeling-verify-btn"
            [disabled]="isLoading() || (!packageForm.get('packageNumbers')?.value?.trim() && !packageForm.get('csvFile')?.value)"
            (click)="onVerifyAndProceed()"
          >
            <ecs-icon *ngIf="isLoading()" name="spinner" size="small" class="spinning"></ecs-icon>
            Verify numbers and proceed
          </ecs-button>
        </div>
      </form>
  </div>

  <!-- Card 2: Verification Results and Label Configuration -->
  <div class="white-card" [class.disabled]="!isVerified()">
    <h2 class="card-title">Verification results and label configuration</h2>
      <!-- Verification Results -->
      <div class="verification-results mb-xl">
        <p class="form-field-label">Verification results:</p>

        <div *ngIf="!isVerified()" class="pending-verification">
          <p class="muted-text">Verify package numbers first to see results here</p>
        </div>

        <div *ngIf="isVerified() && verificationResult()" class="verification-summary">
          <!-- Summary lines -->
          <div class="summary-lines">
            <div class="summary-line success-line" *ngIf="verificationResult()!.totalFound > 0">
              <ecs-icon name="check_circle" size="small" class="success-icon"></ecs-icon>
              <span class="summary-text success">
                Found {{ verificationResult()!.totalFound }} valid package(s) ready for labeling
              </span>
            </div>

            <div class="summary-line error-line" *ngIf="verificationResult()!.totalInvalid > 0">
              <ecs-icon name="cancel" size="small" class="error-icon"></ecs-icon>
              <span class="summary-text error">
                Found {{ verificationResult()!.totalInvalid }} number(s) that were invalid or not found
              </span>
            </div>
          </div>

          <!-- Expandable dropdowns -->
          <div class="expandable-dropdowns" *ngIf="verificationResult()!.totalFound > 0 || verificationResult()!.totalInvalid > 0">
            <div class="dropdown-item" *ngIf="verificationResult()!.totalFound > 0">
              <button type="button" class="dropdown-toggle" (click)="toggleValidPackages()">
                <span class="dropdown-label">Valid packages list</span>
                <ecs-icon [name]="showValidPackages ? 'expand_less' : 'expand_more'" size="small"></ecs-icon>
              </button>
              <div *ngIf="showValidPackages" class="dropdown-content">
                <div *ngFor="let packageNum of verificationResult()!.validPackages" class="package-item">
                  {{ packageNum }}
                </div>
              </div>
            </div>

            <div class="dropdown-item" *ngIf="verificationResult()!.totalInvalid > 0">
              <button type="button" class="dropdown-toggle" (click)="toggleInvalidPackages()">
                <span class="dropdown-label">Invalid or not found numbers</span>
                <ecs-icon [name]="showInvalidPackages ? 'expand_less' : 'expand_more'" size="small"></ecs-icon>
              </button>
              <div *ngIf="showInvalidPackages" class="dropdown-content">
                <div *ngFor="let packageNum of verificationResult()!.invalidPackages" class="package-item invalid">
                  {{ packageNum }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Label Configuration -->
      <form [formGroup]="labelForm" class="label-configuration">
        <p class="form-field-label">How to handle existing labels:</p>
        
        <div [class.disabled-content]="!isVerified()">
          <div class="radio-options">
            <ecs-radio
              formControlName="labelAction"
              value="add"
              label="Add selected label(s), keeping all existing labels"
              class="radio-option" />

            <div class="radio-with-warning">
              <ecs-radio
                formControlName="labelAction"
                value="replace"
                label="Replace all existing labels with only the selected label(s)"
                class="radio-option" />
              <div class="warning-text">
                Warning: This will remove all existing labels from these reservations.
              </div>
            </div>

            <ecs-radio
              formControlName="labelAction"
              value="remove"
              label="Remove all existing labels (without adding new ones)"
              class="radio-option" />
          </div>

          <div class="form-section-container mb-l" *ngIf="labelForm.get('labelAction')?.value !== 'remove'">
            <h3 class="section-header">Label configuration</h3>

            <div class="form-field mb-l">
              <label class="field-label-small">Select label</label>
              <p-select
                class="form-field-container size-xxl"
                formControlName="selectedLabel"
                optionLabel="name"
                optionValue="id"
                placeholder="No selected label"
                [options]="availableLabels()"
                [required]="true"
              ></p-select>
            </div>

            <div class="form-field mb-l">
              <label class="field-label-small">
                Description <span class="optional-text">(Optional)</span>
              </label>
              <ecs-input
                formControlName="description"
                class="form-field-container size-xxl mb-xs"
                [textarea]="true"
                [rows]="3"
                placeholder="Describe shortly situation (max. 240 characters)"
                [maxlength]="240"
                [invalid]="labelForm.get('description')?.touched && !labelForm.get('description')?.valid"
              ></ecs-input>
              <small class="char-count mb-l">
                {{ labelForm.get('description')?.value?.length || 0 }}/240
              </small>
            </div>

            <button type="button" class="add-label-link">
              Add another label
            </button>
          </div>
        </div>

        <div *ngIf="isSuccess()" class="success-message mb-l">
          <ecs-icon name="check_circle" size="small"></ecs-icon>
          <span>Labels successfully applied to {{ verificationResult()!.totalFound }} packages!</span>
        </div>

        <div class="d-flex">
          <ecs-button
            class="mr-xl"
            type="button"
            data-selector="mass-labeling-apply-btn"
            [disabled]="!isVerified() || labelForm.invalid || isLoading()"
            (click)="onApplyLabels()"
          >
            <ecs-icon *ngIf="isLoading()" name="spinner" size="small" class="spinning"></ecs-icon>
            Apply label(s) to {{ verificationResult()?.totalFound || 0 }} packages
          </ecs-button>
          
          <ecs-button
            outline="true"
            kind="tertiary"
            data-selector="mass-labeling-clear-btn"
            (click)="onClear()"
          >
            Clear
          </ecs-button>
        </div>
      </form>
  </div>
</div>

<!-- Dialog for displaying save errors -->
<ui-failed-to-save-dialog #failedToSaveDialog></ui-failed-to-save-dialog>
