<div class="main-container">
  <ecs-button
    class="mb-m"
    [text]="true"
    icon="arrow_left"
    kind="primary"
    size="small"
    data-selector="mass-labeling-back-btn"
    (click)="backToLabels()"
  >
    back to labels
  </ecs-button>
  <div class="form-title-container">
    <div class="d-flex">
      <p class="title">Mass packages labeling</p>
    </div>
  </div>

  <!-- Section 1: Package Input -->
  <div class="expansion-panel mb-xl">
    <div class="expansion-panel-header">
      <div class="title-container">
        <ecs-icon class="title-icon" name="package" />
        <p class="title">Provide package numbers
          <span class="subtitle">Enter package numbers manually or upload a CSV file</span>
        </p>
      </div>
    </div>

    <div class="expansion-panel-content">
      <form [formGroup]="packageForm">
        <!-- Option 1: Manual Input -->
        <div class="form-section-container mb-l">
          <p class="form-field-label">Input method</p>
          <ecs-radio-group formControlName="inputMethod" class="form-radio-group mb-l">
            <ecs-radio value="manual" label="Enter numbers manually" />
            <ecs-radio value="file" label="Upload a CSV file" />
          </ecs-radio-group>
        </div>
        
        <div class="form-section-container mb-l" *ngIf="packageForm.get('inputMethod')?.value === 'manual'">
          <ecs-input
            formControlName="packageNumbers"
            class="form-field-container size-xxl"
            label="Package numbers"
            [required]="true"
            [invalid]="packageForm.get('packageNumbers')?.touched && !packageForm.get('packageNumbers')?.valid"
            [textarea]="true"
            [rows]="4"
            placeholder="1234567, 23678"
          ></ecs-input>
          <small class="help-text">
            E.g., 1234567, 7654321, 9876543 or list each number on a new line
          </small>
        </div>

        <!-- Option 2: File Upload -->
        
        <div class="form-section-container mb-l" *ngIf="packageForm.get('inputMethod')?.value === 'file'">
          <p class="form-field-label">Select CSV File</p>
          <div class="file-upload-container">
            <input 
              type="file" 
              id="csv-file"
              accept=".csv"
              (change)="onFileSelected($event)"
              class="file-input">
            <ecs-button
              outline="true"
              kind="tertiary"
              icon="download"
              size="small"
              (click)="downloadCsvTemplate()"
            >
              Download CSV template
            </ecs-button>
          </div>
          <small class="help-text">
            Accepted format: csv file. The file should contain one reservation number per row in a single column.
          </small>
        </div>

        <div class="d-flex">
          <ecs-button
            type="button"
            data-selector="mass-labeling-verify-btn"
            [disabled]="packageForm.invalid || isLoading()"
            (click)="onVerifyAndProceed()"
          >
            <ecs-icon *ngIf="isLoading()" name="spinner" size="small" class="spinning"></ecs-icon>
            Verify numbers and proceed
          </ecs-button>
        </div>
    </form>
  </div>

  <!-- Section 2: Verification Results and Label Configuration -->
  <div class="expansion-panel mb-xl" [class.disabled]="!isVerified()">
    <div class="expansion-panel-header">
      <div class="title-container">
        <ecs-icon class="title-icon" name="label" />
        <p class="title">Verification results and label configuration
          <span class="subtitle">Configure and apply labels to verified packages</span>
        </p>
      </div>
    </div>

    <div class="expansion-panel-content">
      <!-- Verification Results -->
      <div class="verification-results mb-xl">
        <p class="form-field-label">Verification results:</p>
        
        <div *ngIf="!isVerified()" class="pending-verification">
          <p class="muted-text">Verify package numbers first to see results here</p>
        </div>
        
        <ng-container *ngIf="isVerified() && verificationResult()">
          <div class="expandable-section" *ngIf="verificationResult()!.totalFound > 0">
            <button type="button" class="expand-btn" (click)="showValidPackages = !showValidPackages">
              <ecs-icon [name]="showValidPackages ? 'expand_less' : 'expand_more'" size="small"></ecs-icon>
              <span class="success-text">{{ verificationResult()!.totalFound }} valid package numbers found</span>
            </button>
            <div *ngIf="showValidPackages" class="package-list">
              <div *ngFor="let packageNum of verificationResult()!.validPackages" class="package-item">
                {{ packageNum }}
              </div>
            </div>
          </div>

          <div class="expandable-section" *ngIf="verificationResult()!.totalInvalid > 0">
            <button type="button" class="expand-btn" (click)="showInvalidPackages = !showInvalidPackages">
              <ecs-icon [name]="showInvalidPackages ? 'expand_less' : 'expand_more'" size="small"></ecs-icon>
              <span class="error-text">{{ verificationResult()!.totalInvalid }} invalid package numbers</span>
            </button>
            <div *ngIf="showInvalidPackages" class="package-list">
              <div *ngFor="let packageNum of verificationResult()!.invalidPackages" class="package-item error">
                {{ packageNum }}
              </div>
            </div>
          </div>
        </ng-container>
      </div>

      <!-- Label Configuration -->
      <form [formGroup]="labelForm" class="label-configuration">
        <p class="form-field-label">How to handle existing labels:</p>
        
        <div [class.disabled-content]="!isVerified()">
          <ecs-radio-group formControlName="labelAction" class="form-radio-group mb-l">
            <ecs-radio value="add" label="Add selected label(s), keeping all existing labels" />
            <ecs-radio value="replace" label="Replace all existing labels with only the selected label(s)" />
            <ecs-radio value="remove" label="Remove all existing labels (without adding new ones)" />
          </ecs-radio-group>
          
          <small *ngIf="labelForm.get('labelAction')?.value === 'replace'" class="warning-text mb-l">
            Warning: This will remove all existing labels from these reservations.
          </small>

          <div class="form-section-container mb-l" *ngIf="labelForm.get('labelAction')?.value !== 'remove'">
            <p class="form-field-label">Label configuration</p>
            
            <p-select
              class="form-field-container size-xxl mb-l"
              formControlName="selectedLabel"
              optionLabel="name"
              optionValue="id"
              placeholder="Select a label"
              [options]="availableLabels()"
              [required]="true"
            ></p-select>

            <ecs-input
              formControlName="description"
              class="form-field-container size-xxl mb-s"
              label="Description"
              [required]="true"
              [textarea]="true"
              [rows]="3"
              placeholder="Describe shortly situation (max. 240 characters)"
              [maxlength]="240"
              [invalid]="labelForm.get('description')?.touched && !labelForm.get('description')?.valid"
            ></ecs-input>
            <small class="char-count mb-l">
              {{ labelForm.get('description')?.value?.length || 0 }}/240
            </small>

            <ecs-button
              outline="true"
              kind="tertiary"
              icon="add"
              size="small"
            >
              Add another label
            </ecs-button>
          </div>
        </div>

        <div *ngIf="isSuccess()" class="success-message mb-l">
          <ecs-icon name="check_circle" size="small"></ecs-icon>
          <span>Labels successfully applied to {{ verificationResult()!.totalFound }} packages!</span>
        </div>

        <div class="d-flex">
          <ecs-button
            class="mr-xl"
            type="button"
            data-selector="mass-labeling-apply-btn"
            [disabled]="!isVerified() || labelForm.invalid || isLoading()"
            (click)="onApplyLabels()"
          >
            <ecs-icon *ngIf="isLoading()" name="spinner" size="small" class="spinning"></ecs-icon>
            Apply label(s) to {{ verificationResult()?.totalFound || 0 }} packages
          </ecs-button>
          
          <ecs-button
            outline="true"
            kind="tertiary"
            data-selector="mass-labeling-clear-btn"
            (click)="onClear()"
          >
            Clear
          </ecs-button>
        </div>
      </form>
    </div>


  </div>
</div>

<!-- Dialog for displaying save errors -->
<ui-failed-to-save-dialog #failedToSaveDialog></ui-failed-to-save-dialog>
